'use client'

import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreList, StoreListInput, StoreListItem } from '@ninebot/core'
import {
  TCatchMessage,
  useDebounceFn,
  useLazyGetStoreV2Query,
  useToastContext,
} from '@ninebot/core'
import { resolveCatchMessage } from '@ninebot/core/src/utils/util'

import { Skeleton } from '@/components'

import { performanceMonitor } from './PerformanceMonitor'

// ==================== 类型定义 ====================

/**
 * 位置信息接口
 */
interface LocationResult {
  longitude?: number | null
  latitude?: number | null
}

/**
 * 地址信息接口
 */
interface PdpAddress {
  region?: string
  city?: string
  district?: string
}

/**
 * 组件属性接口
 */
interface StoreListProps {
  store: StoreList
  setStore: (stores: StoreList) => void
  setBtnLoading: (loading: boolean) => void
  curStore: StoreListItem | null
  setCurStore: (store: StoreListItem | null) => void
  productId: number | string
  address?: PdpAddress
  userLocation?: { longitude: number; latitude: number } | null
}

// ==================== 常量定义 ====================

/**
 * 骨架屏配置
 */
const SKELETON_CONFIG = {
  count: 6,
  itemHeight: 80, // 匹配实际门店项高度
  borderRadius: 12,
  backgroundColor: '#F3F3F4',
} as const

// ==================== 图标组件 ====================

/**
 * 选中图标组件
 */
const SelectIcon = () => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.16707 8.9045L2.98856 10.083L7.21427 14.3089L8.39278 14.3089L8.39278 13.1304L4.16707 8.9045Z"
      fill="#DA291C"
    />
    <path
      d="M17.5477 6.67851L16.3691 5.5L8.73858 13.1307L8.73858 14.3092L9.91709 14.3092L17.5477 6.67851Z"
      fill="#DA291C"
    />
  </svg>
)

// ==================== 主组件 ====================

// 简易门店结果缓存（会话级）
const storeCache = new Map<string, StoreListItem[]>()
const storeMsgCache = new Map<string, string>()

function buildStoreCacheKey(productId: number | string, addr?: PdpAddress, loc?: LocationResult) {
  const region = addr?.region || ''
  const city = addr?.city || ''
  const district = addr?.district || ''
  const lng = loc?.longitude || ''
  const lat = loc?.latitude || ''
  return `store:${productId}:${region}|${city}|${district}:${lng},${lat}`
}

/**
 * 门店列表组件
 *
 * 功能：
 * 1. 根据传入的地址信息和位置信息获取门店列表
 * 2. 支持门店选择
 * 3. 提供加载和空状态
 * 4. 使用父组件传入的位置信息，避免重复定位
 * 5. 支持门店消息状态管理
 */
const StoreList = ({
  productId,
  store,
  setStore,
  setBtnLoading,
  curStore,
  setCurStore,
  address,
  userLocation,
}: StoreListProps) => {
  // ==================== 状态管理 ====================

  // UI状态
  const [loading, setLoading] = useState<boolean>(false)
  const [noData, setNoData] = useState<boolean>(false)

  // 门店消息状态
  const [storeMsg, setStoreMsg] = useState<string>('')

  const [getStore] = useLazyGetStoreV2Query()
  const toast = useToastContext()
  const getI18nString = useTranslations('Common')

  // ==================== 计算属性 ====================

  /**
   * 判断门店是否被选中
   */
  const isStoreSelected = useCallback(
    (item: StoreListItem | null): boolean => {
      return !!(curStore && curStore?.store_code === item?.store_code)
    },
    [curStore],
  )

  // ==================== 工具函数 ====================

  /**
   * 构建门店列表请求参数
   */
  const buildStoreListParams = useCallback(
    (pdpLocation: LocationResult | null, pdpAddress?: PdpAddress): StoreListInput | null => {
      const params: StoreListInput = {
        product_id: Number(productId),
      }

      // 添加地址信息
      if (pdpAddress) {
        if (pdpAddress.region) params.region = pdpAddress.region
        if (pdpAddress.city) params.city = pdpAddress.city
        if (pdpAddress.district) params.district = pdpAddress.district
      }

      // 添加位置信息
      if (pdpLocation?.longitude && pdpLocation?.latitude) {
        params.longitude = pdpLocation.longitude.toString()
        params.latitude = pdpLocation.latitude.toString()
      }

      // 确保至少有地址信息或位置信息
      const hasAddressInfo =
        pdpAddress && (pdpAddress.region || pdpAddress.city || pdpAddress.district)
      const hasLocationInfo = pdpLocation?.longitude && pdpLocation?.latitude

      return hasAddressInfo || hasLocationInfo ? params : null
    },
    [productId],
  )

  /**
   * 处理门店列表数据
   */
  const handleStoreListData = useCallback(
    (productStores: (StoreListItem | null)[]) => {
      const filteredStores = productStores.filter(Boolean) as StoreListItem[]
      const storeCount = filteredStores.length

      if (storeCount > 0) {
        setStore(filteredStores)
        setNoData(false)
      } else {
        setStore([])
        setNoData(true)
      }
    },
    [setStore],
  )

  // ==================== 事件处理函数 ====================

  /**
   * 选择门店
   */
  const handleStoreSelect = useCallback(
    (item: StoreListItem) => {
      performanceMonitor.start('门店列表-门店选择交互')

      performanceMonitor.record('门店列表-门店被选中', {
        门店名称: item.store_name,
        门店ID: item.store_id,
        门店地址: item.store_address,
        距离: item.store_distance,
      })

      setCurStore(item)

      performanceMonitor.start('门店列表-滚动到门店')
      const element = document.getElementById(`store-item-${item.store_id}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
      performanceMonitor.end('门店列表-滚动到门店', {
        找到DOM元素: !!element,
        门店ID: item.store_id,
      })

      performanceMonitor.end('门店列表-门店选择交互')
    },
    [setCurStore],
  )

  // ==================== 数据获取逻辑 ====================

  /**
   * 获取门店列表
   */
  const fetchStoreList = useCallback(async () => {
    performanceMonitor.start('门店列表-完整获取流程')

    try {
      performanceMonitor.start('门店列表-状态设置')
      setLoading(true)
      setBtnLoading(true)
      performanceMonitor.end('门店列表-状态设置')

      // 使用传入的地址信息
      const pdpAddress = address

      const pdpLocation: LocationResult | null = userLocation
        ? {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
          }
        : null

      // 读取缓存
      const cacheKey = buildStoreCacheKey(productId, pdpAddress, pdpLocation || undefined)
      const cachedList = storeCache.get(cacheKey)
      const cachedMsg = storeMsgCache.get(cacheKey)
      if (cachedList) {
        performanceMonitor.record('门店列表-命中内存缓存', {
          数量: cachedList.length,
          消息: cachedMsg || '',
        })
        setStore(cachedList)
        setStoreMsg(cachedMsg || '')
        setNoData(cachedList.length === 0)
        // 背景校准：不阻塞 UI，刷新最新数据
        ;(async () => {
          try {
            const paramsBg = buildStoreListParams(pdpLocation, pdpAddress)
            if (!paramsBg) return
            const dataBg = await getStore({ input: paramsBg }).unwrap()
            const rawItems = dataBg?.product_storesV2?.items || []
            const items = rawItems.filter(Boolean) as StoreListItem[]
            const msg = dataBg?.product_storesV2?.message || ''
            storeCache.set(cacheKey, items)
            storeMsgCache.set(cacheKey, msg)
            handleStoreListData(rawItems)
            setStoreMsg(msg)
          } catch {}
        })()
        return
      }

      performanceMonitor.start('门店列表-构建请求参数')
      const params = buildStoreListParams(pdpLocation, pdpAddress)
      performanceMonitor.end('门店列表-构建请求参数', {
        有位置信息: !!pdpLocation,
        有地址信息: !!pdpAddress,
        请求参数: params,
      })

      if (!params) {
        performanceMonitor.record('门店列表-参数不足', {
          地址信息: pdpAddress,
          位置信息: pdpLocation,
        })
        console.warn('门店列表请求参数不足')
        return
      }

      performanceMonitor.start('门店列表-API调用', {
        产品ID: params.product_id,
        地址: pdpAddress ? `${pdpAddress.region} ${pdpAddress.city} ${pdpAddress.district}` : '无',
        坐标: pdpLocation ? `${pdpLocation.longitude}, ${pdpLocation.latitude}` : '无',
      })

      // 超时保护（不侵入 RTK Query 入参）
      const timeoutMs = 5000
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('timeout')), timeoutMs),
      )
      let data
      try {
        data = await Promise.race([getStore({ input: params }).unwrap(), timeoutPromise])
      } catch (err) {
        if (err instanceof Error && err.message === 'timeout') {
          performanceMonitor.record('门店列表-请求超时', { 超时: `${timeoutMs}ms` })
        }
        throw err
      }

      performanceMonitor.end('门店列表-API调用', {
        返回门店数量: data?.product_storesV2?.items?.length || 0,
      })

      performanceMonitor.start('门店列表-数据处理')
      if (data?.product_storesV2) {
        const rawItems = data.product_storesV2.items || []
        const product_stores = rawItems.filter(Boolean) as StoreListItem[]
        const msg = data.product_storesV2.message || ''
        // 写入缓存
        storeCache.set(cacheKey, product_stores)
        storeMsgCache.set(cacheKey, msg)
        setStoreMsg(msg)
        handleStoreListData(product_stores)
      } else {
        setStore([])
        setStoreMsg('')
        setNoData(true)
      }
      performanceMonitor.end('门店列表-数据处理')
    } catch (error) {
      performanceMonitor.record('门店列表-API调用失败', {
        错误信息: String(error),
      })
      console.error('获取门店列表失败:', error)
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
      setStore([])
      setNoData(true)
    } finally {
      setLoading(false)
      setBtnLoading(false)
      performanceMonitor.end('门店列表-完整获取流程')
    }
  }, [
    address,
    userLocation,
    productId,
    buildStoreListParams,
    getStore,
    handleStoreListData,
    setStore,
    setBtnLoading,
    toast,
  ])

  // ==================== 副作用 ====================

  // 防抖的门店列表获取
  const { run: debouncedFetchStoreList } = useDebounceFn(fetchStoreList, { wait: 500 })

  /**
   * 监听产品ID、地址和用户位置变化，重新获取门店列表（使用防抖）
   */
  useEffect(() => {
    debouncedFetchStoreList()

    return () => {
      setLoading(false)
      setBtnLoading(false)
    }
  }, [debouncedFetchStoreList, setBtnLoading])

  // ==================== 渲染函数 ====================

  /**
   * 渲染单个门店项骨架屏
   */
  const renderStoreItemSkeleton = useCallback(
    (index: number) => (
      <Skeleton
        key={index}
        style={{
          width: '100%',
          height: '50px',
          borderRadius: '12px',
          backgroundColor: SKELETON_CONFIG.backgroundColor,
        }}
      />
    ),
    [],
  )

  /**
   * 渲染骨架屏
   */
  const renderLoadingState = useCallback(
    () => (
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-4">
          {Array.from({ length: SKELETON_CONFIG.count }, (_, index) =>
            renderStoreItemSkeleton(index),
          )}
        </div>
      </div>
    ),
    [renderStoreItemSkeleton],
  )

  /**
   * 渲染空状态
   */
  const renderEmptyState = useCallback(
    () => (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center text-lg text-gray-500">
        {storeMsg || getI18nString('product_no_store_tip')}
      </div>
    ),
    [storeMsg, getI18nString],
  )

  /**
   * 渲染门店列表项
   */
  const renderStoreItem = useCallback(
    (item: StoreListItem) => (
      <div
        key={item?.store_id}
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleStoreSelect(item)
        }}
        id={`store-item-${item?.store_id}`}
        className={`flex h-20 cursor-pointer items-center justify-between rounded-base-12 border px-8 transition-colors hover:border-primary ${
          isStoreSelected(item) ? 'border-primary' : 'border-gray-200'
        }`}>
        <div className="flex-1 truncate font-miSansMedium380">{item?.store_name}</div>
        {isStoreSelected(item) && <SelectIcon />}
      </div>
    ),
    [handleStoreSelect, isStoreSelected],
  )

  // ==================== 条件渲染 ====================

  // 渲染加载状态
  if (loading) {
    return renderLoadingState()
  }

  // 渲染空状态
  if (noData && store.length === 0) {
    return renderEmptyState()
  }

  // ==================== 组件返回 ====================

  return (
    <div className="flex-1 overflow-y-auto pr-base-16">
      <div className="space-y-4">
        {(store.filter(Boolean) as StoreListItem[]).map(renderStoreItem)}
      </div>
    </div>
  )
}

export default StoreList
